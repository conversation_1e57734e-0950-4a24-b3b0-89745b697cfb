package com.xtc.marketing.salespromotion.executor.extension.benefit;

import com.alibaba.cola.extension.Extension;
import com.xtc.marketing.salespromotion.dataobject.SalesPromotionDO;
import com.xtc.marketing.salespromotion.dto.BenefitItemDTO;
import com.xtc.marketing.salespromotion.dto.CalculateDTO;
import com.xtc.marketing.salespromotion.dto.PromotionDetailDTO;
import com.xtc.marketing.salespromotion.dto.SalesPromotionDTO;
import com.xtc.marketing.salespromotion.util.BeanCopier;
import com.xtc.marketing.salespromotion.util.GsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Extension(useCase = BenefitExtConstant.USE_CASE, scenario = BenefitExtConstant.SCENARIO_GIFT)
public class GiftBenefitExt implements BenefitExtPt {

    @Override
    public void calculateSingle(SalesPromotionDO salesPromotion, String paramConfig,
                                CalculateDTO calculateDTO, List<String> skuIds) {
        List<BenefitItemDTO> giftBenefitItems = GsonUtil.jsonToList(paramConfig, BenefitItemDTO.class);

        calculateDTO.getCalculateSkus().stream()
                .filter(sku -> skuIds.contains(sku.getSkuId()))
                .forEach(sku -> {
                    PromotionDetailDTO promotionDetail = this.buildPromotionDetail(salesPromotion, giftBenefitItems, sku.getSkuId());
                    sku.addOrUpdatePromotionDetail(promotionDetail);
                });
    }

    @Override
    public void calculateCompose(SalesPromotionDO salesPromotion, String paramConfig,
                                 CalculateDTO calculateDTO, List<String> skuIds) {
        List<BenefitItemDTO> giftBenefitItems = GsonUtil.jsonToList(paramConfig, BenefitItemDTO.class);

        // 记录优惠详情
        String skuIdsStr = String.join(",", skuIds);
        PromotionDetailDTO promotionDetail = this.buildPromotionDetail(salesPromotion, giftBenefitItems, skuIdsStr);
        calculateDTO.addOrUpdatePromotionDetail(promotionDetail);
    }

    @Override
    public void setBenefit(SalesPromotionDTO salesPromotionDTO, String paramConfig) {
        List<BenefitItemDTO> giftBenefitItems = GsonUtil.jsonToList(paramConfig, BenefitItemDTO.class);
        salesPromotionDTO.setGiftSkuList(giftBenefitItems);
    }

    /**
     * 构建优惠详情
     *
     * @param salesPromotion 促销配置
     * @param benefitItems   赠品sku集合
     * @param skuIds         skuId集合
     * @return 优惠详情
     */
    private PromotionDetailDTO buildPromotionDetail(SalesPromotionDO salesPromotion, List<BenefitItemDTO> benefitItems, String skuIds) {
        PromotionDetailDTO detailDTO = BeanCopier.copy(salesPromotion, PromotionDetailDTO::new);
        detailDTO.setSkuIds(skuIds);
        detailDTO.setGiftSkuList(benefitItems);
        return detailDTO;
    }

}
