package com.xtc.marketing.salespromotion;

import com.xtc.marketing.salespromotion.dto.CalculateDTO;
import com.xtc.marketing.salespromotion.dto.SalesPromotionDTO;
import com.xtc.marketing.salespromotion.dto.query.CalculateQry;
import com.xtc.marketing.salespromotion.dto.query.PromotionListQry;

import java.util.List;

public interface SalesPromotionServiceI {

    /**
     * 促销计价
     *
     * @param qry 参数
     * @return 促销计价结果
     */
    CalculateDTO calculate(CalculateQry qry);

    /**
     * 查询促销列表
     *
     * @param qry 参数
     * @return 促销列表
     */
    List<SalesPromotionDTO> listSalesPromotions(PromotionListQry qry);

}
