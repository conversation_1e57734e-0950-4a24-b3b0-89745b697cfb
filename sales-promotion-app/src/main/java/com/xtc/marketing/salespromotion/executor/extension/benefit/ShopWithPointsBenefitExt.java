package com.xtc.marketing.salespromotion.executor.extension.benefit;

import com.alibaba.cola.extension.Extension;
import com.xtc.marketing.salespromotion.dataobject.SalesPromotionDO;
import com.xtc.marketing.salespromotion.dto.CalculateDTO;
import com.xtc.marketing.salespromotion.dto.PromotionDetailDTO;
import com.xtc.marketing.salespromotion.dto.SalesPromotionDTO;
import com.xtc.marketing.salespromotion.util.BeanCopier;
import com.xtc.marketing.salespromotion.util.GsonUtil;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * 积分抵扣权益扩展
 */
@Slf4j
@RequiredArgsConstructor
@Extension(useCase = BenefitExtConstant.USE_CASE, scenario = BenefitExtConstant.SCENARIO_SHOP_WITH_POINTS)
public class ShopWithPointsBenefitExt implements BenefitExtPt {

    @Override
    public void calculateSingle(SalesPromotionDO salesPromotion, String paramConfig,
                                CalculateDTO calculateDTO, List<String> skuIds) {
        Param param = GsonUtil.jsonToBean(paramConfig, Param.class);

        // 计算每个sku的积分抵扣优惠
        calculateDTO.getCalculateSkus().stream()
                .filter(sku -> skuIds.contains(sku.getSkuId()))
                .forEach(sku -> {
                    if (sku.getUsingPoints() != null && sku.getUsingPoints() > 0) {
                        // 计算积分抵扣金额
                        int discountAmount = calculateDeductibleAmount(sku.getUsingPoints(), param.getShopWithPoints());

                        // 计算单品优惠
                        sku.addDiscountAmount(discountAmount);

                        // 记录优惠详情
                        PromotionDetailDTO promotionDetail = this.buildPromotionDetail(salesPromotion,
                                discountAmount, sku.getSkuId(), sku.getUsingPoints());
                        sku.addOrUpdatePromotionDetail(promotionDetail);
                    }
                });

        // 计算总优惠
        int totalDiscountAmount = calculateDTO.getCalculateSkus().stream()
                .filter(sku -> skuIds.contains(sku.getSkuId()))
                .mapToInt(sku -> {
                    if (sku.getUsingPoints() != null && sku.getUsingPoints() > 0) {
                        return calculateDeductibleAmount(sku.getUsingPoints(), param.getShopWithPoints());
                    }
                    return 0;
                })
                .sum();

        calculateDTO.addDiscountAmount(totalDiscountAmount);
    }

    @Override
    public void calculateCompose(SalesPromotionDO salesPromotion, String paramConfig,
                                 CalculateDTO calculateDTO, List<String> skuIds) {
        Param param = GsonUtil.jsonToBean(paramConfig, Param.class);

        // 汇总所有参与促销的sku的积分
        int totalUsingPoints = calculateDTO.getCalculateSkus().stream()
                .filter(sku -> skuIds.contains(sku.getSkuId()))
                .mapToInt(sku -> sku.getUsingPoints() != null ? sku.getUsingPoints() : 0)
                .sum();

        if (totalUsingPoints > 0) {
            // 计算积分抵扣金额
            int discountAmount = calculateDeductibleAmount(totalUsingPoints, param.getShopWithPoints());

            // 计算优惠
            calculateDTO.addDiscountAmount(discountAmount);

            // 记录优惠详情
            String skuIdsStr = String.join(",", skuIds);
            PromotionDetailDTO promotionDetail = this.buildPromotionDetail(salesPromotion,
                    discountAmount, skuIdsStr, totalUsingPoints);
            calculateDTO.addOrUpdatePromotionDetail(promotionDetail);
        }
    }

    @Override
    public void setBenefit(SalesPromotionDTO salesPromotionDTO, String paramConfig) {
        Param param = GsonUtil.jsonToBean(paramConfig, Param.class);
        salesPromotionDTO.setShopWithPoints(param.getShopWithPoints());
    }

    /**
     * 计算积分可抵扣的金额
     *
     * @param usingPoints    使用的积分
     * @param shopWithPoints 多少积分抵扣1元
     * @return 可抵扣的金额（单位：分）
     */
    private int calculateDeductibleAmount(int usingPoints, int shopWithPoints) {
        // 计算可抵扣金额：积分 / 积分兑换比例 * 100（转换为分）
        return BigDecimal.valueOf(usingPoints)
                .divide(BigDecimal.valueOf(shopWithPoints), 2, RoundingMode.DOWN)
                .multiply(BigDecimal.valueOf(100))
                .intValue();
    }

    /**
     * 构建优惠详情
     *
     * @param salesPromotion 促销配置
     * @param discountAmount 优惠金额
     * @param skuIds         skuId集合
     * @param usingPoints    使用的积分
     * @return 优惠详情
     */
    private PromotionDetailDTO buildPromotionDetail(SalesPromotionDO salesPromotion, int discountAmount,
                                                    String skuIds, int usingPoints) {
        PromotionDetailDTO detailDTO = BeanCopier.copy(salesPromotion, PromotionDetailDTO::new);
        detailDTO.setSkuIds(skuIds);
        detailDTO.setDiscountAmount(discountAmount);
        detailDTO.setUsingPoints(usingPoints);
        return detailDTO;
    }

    /**
     * 积分抵扣权益参数
     */
    @Getter
    @Setter
    @ToString
    public static class Param {

        /**
         * 多少积分抵扣1元
         */
        private Integer shopWithPoints;

    }

}
