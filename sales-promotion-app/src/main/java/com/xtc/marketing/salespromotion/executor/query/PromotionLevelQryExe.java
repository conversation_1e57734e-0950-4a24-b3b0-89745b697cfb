package com.xtc.marketing.salespromotion.executor.query;

import com.google.common.collect.Sets;
import com.xtc.marketing.salespromotion.constant.BizConstant;
import com.xtc.marketing.salespromotion.dto.query.SkuQry;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

@Slf4j
@RequiredArgsConstructor
@Component
public class PromotionLevelQryExe {

    /**
     * 获取sku的促销级别
     *
     * @param sku sku
     * @return 促销级别
     */
    public Set<String> execute(SkuQry sku) {
        // 创建促销级别
        Set<String> promotionLevel = this.newPromotionLevel();
        // 填充sku的促销级别
        this.setPromotionLevel(promotionLevel, sku);
        return promotionLevel;
    }

    /**
     * 获取sku的促销级别
     *
     * @param skus sku集合
     * @return 促销级别
     */
    public Set<String> execute(List<SkuQry> skus) {
        // 创建促销级别
        Set<String> promotionLevel = this.newPromotionLevel();
        // 填充sku的促销级别
        skus.forEach(sku -> this.setPromotionLevel(promotionLevel, sku));
        return promotionLevel;
    }

    /**
     * 填充sku的促销级别
     *
     * @param promotionLevel 促销级别
     * @param sku            sku
     */
    private void setPromotionLevel(Set<String> promotionLevel, SkuQry sku) {
        // 代理级别
        if (StringUtils.isNotBlank(sku.getAgentCode())) {
            promotionLevel.add(sku.getAgentCode());
        }
        // 二级代理级别
        if (StringUtils.isNotBlank(sku.getSecondAgentCode())) {
            promotionLevel.add(sku.getSecondAgentCode());
        }
        // 门店级别
        if (StringUtils.isNotBlank(sku.getShopId())) {
            promotionLevel.add(sku.getShopId());
        }
    }

    /**
     * 创建促销级别
     *
     * @return 促销级别
     */
    private Set<String> newPromotionLevel() {
        // 默认：工厂级别
        return Sets.newHashSet(BizConstant.PROMOTION_LEVEL_FACTORY);
    }

}
