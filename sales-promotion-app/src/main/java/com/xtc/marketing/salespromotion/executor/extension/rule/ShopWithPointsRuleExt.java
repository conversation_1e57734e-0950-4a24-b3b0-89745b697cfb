package com.xtc.marketing.salespromotion.executor.extension.rule;

import com.alibaba.cola.extension.Extension;
import com.xtc.marketing.salespromotion.dto.query.SkuQry;
import com.xtc.marketing.salespromotion.executor.extension.benefit.ShopWithPointsBenefitExt;
import com.xtc.marketing.salespromotion.util.GsonUtil;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * 积分抵扣规则扩展
 */
@Slf4j
@RequiredArgsConstructor
@Extension(useCase = RuleExtConstant.USE_CASE, scenario = RuleExtConstant.SCENARIO_SHOP_WITH_POINTS)
public class ShopWithPointsRuleExt implements RuleExtPt {

    @Override
    public boolean checkSingle(String paramConfig, List<SkuQry> skus, String benefitConfig) {
        Param param = GsonUtil.jsonToBean(paramConfig, Param.class);
        // 从权益配置中解析积分兑换比例
        int pointsPerYuan = extractPointsPerYuan(benefitConfig);

        // 判断每个sku是否满足规则，删除不满足规则的sku
        skus.removeIf(sku -> {
            if (sku.getUsingPoints() == null || sku.getUsingPoints() <= 0) {
                return true; // 没有使用积分，不符合规则
            }

            // 计算积分对应的金额范围
            int[] amountRange = calculateAmountRange(param, sku.getSkuUnitPrice());
            int minAmount = amountRange[0];
            int maxAmount = amountRange[1];

            // 计算积分可抵扣的金额
            int deductibleAmount = calculateDeductibleAmount(sku.getUsingPoints(), pointsPerYuan);

            // 判断积分抵扣金额是否在范围内
            return deductibleAmount < minAmount || deductibleAmount > maxAmount;
        });

        return !skus.isEmpty();
    }

    @Override
    public boolean checkCompose(String paramConfig, List<SkuQry> skus, String benefitConfig) {
        Param param = GsonUtil.jsonToBean(paramConfig, Param.class);
        // 从权益配置中解析积分兑换比例
        int pointsPerYuan = extractPointsPerYuan(benefitConfig);

        // 汇总所有积分
        int totalUsingPoints = skus.stream()
                .mapToInt(sku -> sku.getUsingPoints() != null ? sku.getUsingPoints() : 0)
                .sum();

        if (totalUsingPoints <= 0) {
            return false; // 没有使用积分，不符合规则
        }

        // 计算积分对应的金额范围
        int[] amountRange;
        if (param.getCheckType() == CheckType.AMOUNT) {
            // 按金额：直接使用配置的范围
            amountRange = new int[]{param.getMin(), param.getMax()};
        } else {
            // 按比例：计算所有商品的总价，然后按比例计算范围
            int totalSkuAmount = skus.stream()
                    .mapToInt(sku -> sku.getSkuUnitPrice() * sku.getSkuNum())
                    .sum();

            BigDecimal minRatio = BigDecimal.valueOf(param.getMin()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
            BigDecimal maxRatio = BigDecimal.valueOf(param.getMax()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);

            int minAmount = BigDecimal.valueOf(totalSkuAmount).multiply(minRatio).intValue();
            int maxAmount = BigDecimal.valueOf(totalSkuAmount).multiply(maxRatio).intValue();

            amountRange = new int[]{minAmount, maxAmount};
        }

        // 计算积分可抵扣的金额
        int deductibleAmount = calculateDeductibleAmount(totalUsingPoints, pointsPerYuan);

        // 判断积分抵扣金额是否在范围内
        return deductibleAmount >= amountRange[0] && deductibleAmount <= amountRange[1];
    }

    /**
     * 从权益配置中提取积分兑换比例
     *
     * @param benefitConfig 权益配置JSON
     * @return 积分兑换比例（多少积分抵扣1元）
     */
    private int extractPointsPerYuan(String benefitConfig) {
        try {
            // 权益配置是一个JSON数组，包含多个权益项
            // 需要找到shopWithPoints类型的权益项
            List<BenefitConfig> benefitConfigs = GsonUtil.jsonToList(benefitConfig, BenefitConfig.class);
            for (BenefitConfig item : benefitConfigs) {
                if ("shopWithPoints".equals(item.getBenefitCode())) {
                    ShopWithPointsBenefitExt.Param param = GsonUtil.jsonToBean(item.getParam(), ShopWithPointsBenefitExt.Param.class);
                    return param.getShopWithPoints();
                }
            }
            // 如果没有找到，返回默认值100（100积分抵扣1元）
            return 100;
        } catch (Exception e) {
            log.warn("解析权益配置失败，使用默认积分兑换比例: {}", benefitConfig, e);
            return 100;
        }
    }

    /**
     * 计算金额范围
     *
     * @param param        规则参数
     * @param skuUnitPrice 商品单价
     * @return 金额范围 [最小金额, 最大金额]
     */
    private int[] calculateAmountRange(Param param, int skuUnitPrice) {
        if (param.getCheckType() == CheckType.AMOUNT) {
            // 按金额：直接使用配置的范围
            return new int[]{param.getMin(), param.getMax()};
        } else {
            // 按比例：根据商品单价计算范围
            BigDecimal minRatio = BigDecimal.valueOf(param.getMin()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
            BigDecimal maxRatio = BigDecimal.valueOf(param.getMax()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);

            int minAmount = BigDecimal.valueOf(skuUnitPrice).multiply(minRatio).intValue();
            int maxAmount = BigDecimal.valueOf(skuUnitPrice).multiply(maxRatio).intValue();

            return new int[]{minAmount, maxAmount};
        }
    }

    /**
     * 计算积分可抵扣的金额
     *
     * @param usingPoints   使用的积分
     * @param pointsPerYuan 多少积分抵扣1元
     * @return 可抵扣的金额（单位：分）
     */
    private int calculateDeductibleAmount(int usingPoints, int pointsPerYuan) {
        // 计算可抵扣金额：积分 / 积分兑换比例 * 100（转换为分）
        return BigDecimal.valueOf(usingPoints)
                .divide(BigDecimal.valueOf(pointsPerYuan), 2, RoundingMode.DOWN)
                .multiply(BigDecimal.valueOf(100))
                .intValue();
    }

    /**
     * 权益配置
     */
    @Getter
    @Setter
    @ToString
    static class BenefitConfig {

        /**
         * 权益代码
         */
        private String benefitCode;
        /**
         * 权益参数
         */
        private String param;

    }

    /**
     * 积分抵扣规则参数
     */
    @Getter
    @Setter
    @ToString
    static class Param {

        /**
         * 检查类型：AMOUNT-按金额，RATIO-按比例
         */
        private CheckType checkType;
        /**
         * 最小值
         * <pre>
         * 当checkType为AMOUNT时，单位为分
         * 当checkType为RATIO时，单位为百分比（例如：15表示15%）
         * </pre>
         */
        private Integer min;
        /**
         * 最大值
         * <pre>
         * 当checkType为AMOUNT时，单位为分
         * 当checkType为RATIO时，单位为百分比（例如：15表示15%）
         * </pre>
         */
        private Integer max;

    }

    /**
     * 检查类型
     */
    @Getter
    enum CheckType {
        AMOUNT("按金额"),
        RATIO("按比例");

        CheckType(String desc) {
            this.desc = desc;
        }

        private final String desc;
    }

}
