package com.xtc.marketing.salespromotion.executor.extension.rule;

import com.alibaba.cola.extension.Extension;
import com.xtc.marketing.salespromotion.dto.query.SkuQry;
import com.xtc.marketing.salespromotion.executor.extension.benefit.BenefitExtConstant;
import com.xtc.marketing.salespromotion.executor.extension.benefit.ShopWithPointsBenefitExt;
import com.xtc.marketing.salespromotion.util.GsonUtil;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 积分抵扣规则扩展
 */
@Slf4j
@RequiredArgsConstructor
@Extension(useCase = RuleExtConstant.USE_CASE, scenario = RuleExtConstant.SCENARIO_SHOP_WITH_POINTS)
public class ShopWithPointsRuleExt implements RuleExtPt {

    /**
     * 默认积分兑换比例（100积分抵扣1元）
     */
    private static final int DEFAULT_POINTS_PER_YUAN = 100;
    /**
     * 百分比转换基数
     */
    private static final int PERCENTAGE_BASE = 100;

    @Override
    public boolean checkSingle(String paramConfig, List<SkuQry> skus, String benefitConfig) {
        Param param = GsonUtil.jsonToBean(paramConfig, Param.class);
        int pointsPerYuan = extractPointsPerYuan(benefitConfig);
        // 判断每个sku是否满足规则，删除不满足规则的sku
        skus.removeIf(sku -> !allowSingleSku(sku, param, pointsPerYuan));
        return !skus.isEmpty();
    }

    @Override
    public boolean checkCompose(String paramConfig, List<SkuQry> skus, String benefitConfig) {
        Param param = GsonUtil.jsonToBean(paramConfig, Param.class);
        int pointsPerYuan = extractPointsPerYuan(benefitConfig);

        // 汇总所有积分
        int totalUsingPoints = calculateTotalUsingPoints(skus);
        if (totalUsingPoints <= 0) {
            return false; // 没有使用积分，不符合规则
        }

        // 计算积分对应的金额范围
        int totalSkuAmount = calculateTotalSkuAmount(skus);
        int[] amountRange = calculateAmountRangeForCompose(param, totalSkuAmount);

        // 计算积分可抵扣的金额
        int deductibleAmount = calculateDeductibleAmount(totalUsingPoints, pointsPerYuan);

        // 判断积分抵扣金额是否在范围内
        return deductibleAmount >= amountRange[0] && deductibleAmount <= amountRange[1];
    }

    /**
     * 检查单个SKU是否符合积分抵扣规则
     */
    private boolean allowSingleSku(SkuQry sku, Param param, int pointsPerYuan) {
        if (sku.getUsingPoints() == null || sku.getUsingPoints() <= 0) {
            return false; // 没有使用积分，不符合规则
        }

        // 计算SKU总价（单价 × 数量）
        int skuTotalPrice = sku.getSkuUnitPrice() * sku.getSkuNum();

        // 计算积分对应的金额范围
        int[] amountRange = calculateAmountRangeForSku(param, skuTotalPrice);

        // 计算积分可抵扣的金额
        int deductibleAmount = calculateDeductibleAmount(sku.getUsingPoints(), pointsPerYuan);

        // 判断积分抵扣金额是否在范围内
        return deductibleAmount >= amountRange[0] && deductibleAmount <= amountRange[1];
    }

    /**
     * 计算所有SKU的总使用积分
     */
    private int calculateTotalUsingPoints(List<SkuQry> skus) {
        return skus.stream()
                .mapToInt(sku -> sku.getUsingPoints() != null ? sku.getUsingPoints() : 0)
                .sum();
    }

    /**
     * 计算所有SKU的总金额
     */
    private int calculateTotalSkuAmount(List<SkuQry> skus) {
        return skus.stream()
                .mapToInt(sku -> sku.getSkuUnitPrice() * sku.getSkuNum())
                .sum();
    }

    /**
     * 计算组合优惠的金额范围
     */
    private int[] calculateAmountRangeForCompose(Param param, int totalSkuAmount) {
        if (param.getCheckType() == CheckType.AMOUNT) {
            return new int[]{param.getMin(), param.getMax()};
        } else {
            return calculateRatioBasedAmountRange(param, totalSkuAmount);
        }
    }

    /**
     * 计算单个SKU的金额范围
     *
     * @param param         规则参数
     * @param skuTotalPrice SKU总价（单价 × 数量）
     * @return 金额范围 [最小金额, 最大金额]
     */
    private int[] calculateAmountRangeForSku(Param param, int skuTotalPrice) {
        if (param.getCheckType() == CheckType.AMOUNT) {
            return new int[]{param.getMin(), param.getMax()};
        } else {
            return calculateRatioBasedAmountRange(param, skuTotalPrice);
        }
    }

    /**
     * 基于比例计算金额范围
     */
    private int[] calculateRatioBasedAmountRange(Param param, int baseAmount) {
        BigDecimal minRatio = BigDecimal.valueOf(param.getMin())
                .divide(BigDecimal.valueOf(PERCENTAGE_BASE), 2, RoundingMode.HALF_DOWN);
        BigDecimal maxRatio = BigDecimal.valueOf(param.getMax())
                .divide(BigDecimal.valueOf(PERCENTAGE_BASE), 2, RoundingMode.HALF_DOWN);

        int minAmount = BigDecimal.valueOf(baseAmount).multiply(minRatio).intValue();
        int maxAmount = BigDecimal.valueOf(baseAmount).multiply(maxRatio).intValue();

        return new int[]{minAmount, maxAmount};
    }

    /**
     * 计算积分可抵扣的金额
     *
     * @param usingPoints   使用的积分
     * @param pointsPerYuan 多少积分抵扣1元
     * @return 可抵扣的金额（单位：分）
     */
    private int calculateDeductibleAmount(int usingPoints, int pointsPerYuan) {
        if (usingPoints <= 0 || pointsPerYuan <= 0) {
            return 0;
        }

        // 计算可抵扣金额：积分 / 积分兑换比例 * 100（转换为分）
        return BigDecimal.valueOf(usingPoints)
                .divide(BigDecimal.valueOf(pointsPerYuan), 2, RoundingMode.DOWN)
                .movePointRight(2)
                .intValue();
    }

    /**
     * 从权益配置中提取积分兑换比例
     *
     * @param benefitConfig 权益配置JSON
     * @return 积分兑换比例（多少积分抵扣1元）
     */
    private int extractPointsPerYuan(String benefitConfig) {
        if (StringUtils.isBlank(benefitConfig)) {
            log.warn("权益配置为空，使用默认积分兑换比例: {}", DEFAULT_POINTS_PER_YUAN);
            return DEFAULT_POINTS_PER_YUAN;
        }
        List<BenefitConfig> benefitConfigs = GsonUtil.jsonToList(benefitConfig, BenefitConfig.class);
        if (CollectionUtils.isEmpty(benefitConfigs)) {
            log.warn("权益配置为空，使用默认积分兑换比例: {}", DEFAULT_POINTS_PER_YUAN);
            return DEFAULT_POINTS_PER_YUAN;
        }
        List<BenefitConfig> listShopWithPointsBenefit = benefitConfigs.stream()
                .filter(item -> BenefitExtConstant.SCENARIO_SHOP_WITH_POINTS.equals(item.getBenefitCode()))
                .collect(Collectors.toList());
        if (listShopWithPointsBenefit.size() > 1) {
            log.warn("存在多个积分抵扣权益配置，使用默认积分兑换比例: {}", DEFAULT_POINTS_PER_YUAN);
            return DEFAULT_POINTS_PER_YUAN;
        }
        BenefitConfig shopWithPointsBenefit = listShopWithPointsBenefit.get(0);
        ShopWithPointsBenefitExt.Param param = GsonUtil.jsonToBean(shopWithPointsBenefit.getParam(), ShopWithPointsBenefitExt.Param.class);
        Integer shopWithPoints = param.getShopWithPoints();
        if (shopWithPoints == null || shopWithPoints <= 0) {
            log.warn("未找到积分抵扣权益配置，使用默认积分兑换比例: {}", DEFAULT_POINTS_PER_YUAN);
            return DEFAULT_POINTS_PER_YUAN;
        }
        return shopWithPoints;
    }

    /**
     * 权益配置
     */
    @Getter
    @Setter
    @ToString
    static class BenefitConfig {

        /**
         * 权益代码
         */
        private String benefitCode;
        /**
         * 权益参数
         */
        private String param;

    }

    /**
     * 积分抵扣规则参数
     */
    @Getter
    @Setter
    @ToString
    static class Param {

        /**
         * 检查类型：AMOUNT-按金额，RATIO-按比例
         */
        private CheckType checkType;
        /**
         * 最小值
         * <pre>
         * 当checkType为AMOUNT时，单位为分
         * 当checkType为RATIO时，单位为百分比（例如：15表示15%）
         * </pre>
         */
        private Integer min;
        /**
         * 最大值
         * <pre>
         * 当checkType为AMOUNT时，单位为分
         * 当checkType为RATIO时，表示最大金额限制，单位为分
         * </pre>
         */
        private Integer max;

    }

    /**
     * 检查类型
     */
    @Getter
    enum CheckType {
        AMOUNT("按金额"),
        RATIO("按比例");

        CheckType(String desc) {
            this.desc = desc;
        }

        private final String desc;
    }

}
