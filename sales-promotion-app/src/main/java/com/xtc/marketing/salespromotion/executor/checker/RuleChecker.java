package com.xtc.marketing.salespromotion.executor.checker;

import com.alibaba.cola.extension.BizScenario;
import com.alibaba.cola.extension.ExtensionExecutor;
import com.xtc.marketing.salespromotion.bo.RuleBO;
import com.xtc.marketing.salespromotion.dataobject.SalesPromotionDO;
import com.xtc.marketing.salespromotion.dto.query.SkuQry;
import com.xtc.marketing.salespromotion.enums.PromotionType;
import com.xtc.marketing.salespromotion.exception.BizException;
import com.xtc.marketing.salespromotion.executor.extension.rule.RuleExtConstant;
import com.xtc.marketing.salespromotion.executor.extension.rule.RuleExtPt;
import com.xtc.marketing.salespromotion.util.GsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Component
public class RuleChecker {

    private final ExtensionExecutor extensionExecutor;

    /**
     * 确认符合规则
     *
     * @param salesPromotion 促销配置
     * @param skus           sku集合
     */
    public void check(SalesPromotionDO salesPromotion, List<SkuQry> skus) {
        List<RuleBO> rules = GsonUtil.jsonToList(salesPromotion.getRuleConfig(), RuleBO.class);
        rules.forEach(rule -> {
            // 规则扩展点执行检查
            BizScenario ruleScenario = BizScenario.valueOf(BizScenario.DEFAULT_BIZ_ID,
                    RuleExtConstant.USE_CASE, rule.getRuleCode());
            // 对于不同的促销类型执行不同的检查逻辑
            boolean allow = false;
            if (salesPromotion.getPromotionType() == PromotionType.SINGLE) {
                allow = extensionExecutor.execute(RuleExtPt.class, ruleScenario,
                        extension -> extension.checkSingle(rule.getParam(), skus, salesPromotion.getBenefitConfig()));
            } else if (salesPromotion.getPromotionType() == PromotionType.COMPOSE) {
                allow = extensionExecutor.execute(RuleExtPt.class, ruleScenario,
                        extension -> extension.checkCompose(rule.getParam(), skus, salesPromotion.getBenefitConfig()));
            }
            if (BooleanUtils.isFalse(allow)) {
                throw BizException.of("不满足促销规则 " + rule);
            }
        });
    }

}
