package com.xtc.marketing.salespromotion.executor.query;

import com.alibaba.cola.extension.BizScenario;
import com.alibaba.cola.extension.ExtensionExecutor;
import com.xtc.marketing.salespromotion.bo.BenefitBO;
import com.xtc.marketing.salespromotion.dataobject.SalesPromotionDO;
import com.xtc.marketing.salespromotion.dto.SalesPromotionDTO;
import com.xtc.marketing.salespromotion.dto.query.PromotionListQry;
import com.xtc.marketing.salespromotion.dto.query.SkuQry;
import com.xtc.marketing.salespromotion.exception.BizException;
import com.xtc.marketing.salespromotion.executor.checker.ScopeChecker;
import com.xtc.marketing.salespromotion.executor.extension.benefit.BenefitExtConstant;
import com.xtc.marketing.salespromotion.executor.extension.benefit.BenefitExtPt;
import com.xtc.marketing.salespromotion.repository.SalesPromotionRepository;
import com.xtc.marketing.salespromotion.util.BeanCopier;
import com.xtc.marketing.salespromotion.util.GsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Component
public class SalesPromotionsQryExe {

    private final SalesPromotionRepository salesPromotionRepository;
    private final PromotionLevelQryExe promotionLevelQryExe;
    private final ScopeChecker scopeChecker;
    private final ExtensionExecutor extensionExecutor;

    public List<SalesPromotionDTO> execute(PromotionListQry qry) {
        // 获取促销配置集合
        List<SalesPromotionDO> salesPromotions = this.getPromotions(qry);

        // 过滤出符合范围的促销配置，类型转换
        return salesPromotions.stream()
                .filter(salesPromotionDO -> this.filterScopeSkus(salesPromotionDO, qry))
                .map(salesPromotionDO -> {
                    SalesPromotionDTO salesPromotionDTO = BeanCopier.copy(salesPromotionDO, SalesPromotionDTO::new);
                    List<BenefitBO> benefits = GsonUtil.jsonToList(salesPromotionDO.getBenefitConfig(), BenefitBO.class);
                    benefits.forEach(benefit -> {
                        BizScenario benefitScenario = BizScenario.valueOf(BizScenario.DEFAULT_BIZ_ID,
                                BenefitExtConstant.USE_CASE, benefit.getBenefitCode());

                        extensionExecutor.executeVoid(BenefitExtPt.class, benefitScenario,
                                extension -> extension.setBenefit(salesPromotionDTO, benefit.getParam()));
                    });
                    return salesPromotionDTO;
                })
                .collect(Collectors.toList());
    }

    /**
     * 确认sku符合促销范围
     *
     * @param promotion 促销配置
     * @param sku       sku
     */
    private boolean filterScopeSkus(SalesPromotionDO promotion, SkuQry sku) {
        try {
            scopeChecker.check(promotion.getPromotionScope(), sku);
            return true;
        } catch (BizException e) {
            log.debug("不符合促销范围 promotionToken: {}, sku: {}", promotion.getPromotionToken(), sku);
            return false;
        } catch (Exception e) {
            log.error("判断促销范围异常 promotionToken: {}", promotion.getPromotionToken(), e);
            return false;
        }
    }

    /**
     * 获取促销配置集合
     *
     * @param qry 参数
     * @return 促销配置集合
     */
    private List<SalesPromotionDO> getPromotions(PromotionListQry qry) {
        Set<String> promotionLevel = promotionLevelQryExe.execute(qry);
        return salesPromotionRepository.listEnabledByLevel(qry.getBizCode(), LocalDateTime.now(), promotionLevel);
    }

}
