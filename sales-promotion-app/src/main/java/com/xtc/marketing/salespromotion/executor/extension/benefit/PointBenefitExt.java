package com.xtc.marketing.salespromotion.executor.extension.benefit;

import com.alibaba.cola.extension.Extension;
import com.xtc.marketing.salespromotion.dataobject.SalesPromotionDO;
import com.xtc.marketing.salespromotion.dto.CalculateDTO;
import com.xtc.marketing.salespromotion.dto.PromotionDetailDTO;
import com.xtc.marketing.salespromotion.dto.SalesPromotionDTO;
import com.xtc.marketing.salespromotion.util.BeanCopier;
import com.xtc.marketing.salespromotion.util.GsonUtil;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Extension(useCase = BenefitExtConstant.USE_CASE, scenario = BenefitExtConstant.SCENARIO_POINT)
public class PointBenefitExt implements BenefitExtPt {

    @Override
    public void calculateSingle(SalesPromotionDO salesPromotion, String paramConfig,
                                CalculateDTO calculateDTO, List<String> skuIds) {
        Param param = GsonUtil.jsonToBean(paramConfig, Param.class);

        calculateDTO.getCalculateSkus().stream()
                .filter(sku -> skuIds.contains(sku.getSkuId()))
                .forEach(sku -> {
                    PromotionDetailDTO promotionDetail = this.buildPromotionDetail(salesPromotion, param.getPoint(), sku.getSkuId());
                    sku.addOrUpdatePromotionDetail(promotionDetail);
                });
    }

    @Override
    public void calculateCompose(SalesPromotionDO salesPromotion, String paramConfig,
                                 CalculateDTO calculateDTO, List<String> skuIds) {
        Param param = GsonUtil.jsonToBean(paramConfig, Param.class);

        // 记录优惠详情
        String skuIdsStr = String.join(",", skuIds);
        PromotionDetailDTO promotionDetail = this.buildPromotionDetail(salesPromotion, param.getPoint(), skuIdsStr);
        calculateDTO.addOrUpdatePromotionDetail(promotionDetail);
    }

    @Override
    public void setBenefit(SalesPromotionDTO salesPromotionDTO, String paramConfig) {
        Param param = GsonUtil.jsonToBean(paramConfig, Param.class);
        salesPromotionDTO.setPoint(param.getPoint());
    }

    /**
     * 构建优惠详情
     *
     * @param salesPromotion 促销配置
     * @param point          积分数量
     * @param skuIds         skuId集合
     * @return 优惠详情
     */
    private PromotionDetailDTO buildPromotionDetail(SalesPromotionDO salesPromotion, int point, String skuIds) {
        PromotionDetailDTO detailDTO = BeanCopier.copy(salesPromotion, PromotionDetailDTO::new);
        detailDTO.setSkuIds(skuIds);
        detailDTO.setPoint(point);
        return detailDTO;
    }

    /**
     * 赠送积分参数
     */
    @Getter
    @Setter
    @ToString
    static class Param {

        /**
         * 积分数量
         */
        private Integer point;

    }

}
