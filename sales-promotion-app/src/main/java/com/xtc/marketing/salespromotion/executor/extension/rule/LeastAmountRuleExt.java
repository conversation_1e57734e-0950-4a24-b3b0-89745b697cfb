package com.xtc.marketing.salespromotion.executor.extension.rule;

import com.alibaba.cola.extension.Extension;
import com.xtc.marketing.salespromotion.dto.query.SkuQry;
import com.xtc.marketing.salespromotion.util.GsonUtil;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Extension(useCase = RuleExtConstant.USE_CASE, scenario = RuleExtConstant.SCENARIO_LEAST_AMOUNT)
public class LeastAmountRuleExt implements RuleExtPt {

    @Override
    public boolean checkSingle(String paramConfig, List<SkuQry> skus, String benefitConfig) {
        Param param = GsonUtil.jsonToBean(paramConfig, Param.class);
        // 判断每个sku是否满足规则，删除不满足规则的sku
        skus.removeIf(sku -> sku.getSkuUnitPrice() < param.getLeastAmount());
        return !skus.isEmpty();
    }

    @Override
    public boolean checkCompose(String paramConfig, List<SkuQry> skus, String benefitConfig) {
        Param param = GsonUtil.jsonToBean(paramConfig, Param.class);
        int skuTotalAmount = skus.stream().mapToInt(sku -> sku.getSkuUnitPrice() * sku.getSkuNum()).sum();
        return skuTotalAmount >= param.getLeastAmount();
    }

    /**
     * 满X元参数
     */
    @Getter
    @Setter
    @ToString
    static class Param {

        /**
         * 满X元
         */
        private Integer leastAmount;

    }

}
