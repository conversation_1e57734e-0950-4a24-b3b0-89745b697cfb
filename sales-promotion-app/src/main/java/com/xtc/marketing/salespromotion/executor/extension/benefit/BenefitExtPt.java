package com.xtc.marketing.salespromotion.executor.extension.benefit;

import com.alibaba.cola.extension.ExtensionPointI;
import com.xtc.marketing.salespromotion.dataobject.SalesPromotionDO;
import com.xtc.marketing.salespromotion.dto.CalculateDTO;
import com.xtc.marketing.salespromotion.dto.SalesPromotionDTO;

import java.util.List;

/**
 * 权益扩展点
 */
public interface BenefitExtPt extends ExtensionPointI {

    /**
     * 计算现金权益金额（单品优惠）
     *
     * @param salesPromotion 促销配置
     * @param paramConfig    参数配置
     * @param calculateDTO   促销计价结果
     * @param skuIds         参与促销的skuid集合
     */
    void calculateSingle(SalesPromotionDO salesPromotion, String paramConfig,
                         CalculateDTO calculateDTO, List<String> skuIds);

    /**
     * 计算现金权益金额（组合优惠）
     *
     * @param salesPromotion 促销配置
     * @param paramConfig    参数配置
     * @param calculateDTO   促销计价结果
     * @param skuIds         参与促销的skuid集合
     */
    void calculateCompose(SalesPromotionDO salesPromotion, String paramConfig,
                          CalculateDTO calculateDTO, List<String> skuIds);

    /**
     * 为DTO设置权益
     *
     * @param salesPromotionDTO 目标DTO
     * @param paramConfig       参数配置
     */
    void setBenefit(SalesPromotionDTO salesPromotionDTO, String paramConfig);

}
