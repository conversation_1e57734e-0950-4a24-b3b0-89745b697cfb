package com.xtc.marketing.salespromotion.executor.extension.benefit;

import com.alibaba.cola.extension.Extension;
import com.xtc.marketing.salespromotion.dataobject.SalesPromotionDO;
import com.xtc.marketing.salespromotion.dto.BenefitItemDTO;
import com.xtc.marketing.salespromotion.dto.CalculateDTO;
import com.xtc.marketing.salespromotion.dto.PromotionDetailDTO;
import com.xtc.marketing.salespromotion.dto.SalesPromotionDTO;
import com.xtc.marketing.salespromotion.util.BeanCopier;
import com.xtc.marketing.salespromotion.util.GsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Extension(useCase = BenefitExtConstant.USE_CASE, scenario = BenefitExtConstant.SCENARIO_COUPON)
public class CouponBenefitExt implements BenefitExtPt {

    @Override
    public void calculateSingle(SalesPromotionDO salesPromotion, String paramConfig,
                                CalculateDTO calculateDTO, List<String> skuIds) {
        List<BenefitItemDTO> couponBenefitItems = GsonUtil.jsonToList(paramConfig, BenefitItemDTO.class);

        calculateDTO.getCalculateSkus().stream()
                .filter(sku -> skuIds.contains(sku.getSkuId()))
                .forEach(sku -> {
                    PromotionDetailDTO promotionDetail = this.buildPromotionDetail(salesPromotion, couponBenefitItems, sku.getSkuId());
                    sku.addOrUpdatePromotionDetail(promotionDetail);
                });
    }

    @Override
    public void calculateCompose(SalesPromotionDO salesPromotion, String paramConfig,
                                 CalculateDTO calculateDTO, List<String> skuIds) {
        List<BenefitItemDTO> couponBenefitItems = GsonUtil.jsonToList(paramConfig, BenefitItemDTO.class);

        // 记录优惠详情
        String skuIdsStr = String.join(",", skuIds);
        PromotionDetailDTO promotionDetail = this.buildPromotionDetail(salesPromotion, couponBenefitItems, skuIdsStr);
        calculateDTO.addOrUpdatePromotionDetail(promotionDetail);
    }

    @Override
    public void setBenefit(SalesPromotionDTO salesPromotionDTO, String paramConfig) {
        List<BenefitItemDTO> couponBenefitItems = GsonUtil.jsonToList(paramConfig, BenefitItemDTO.class);
        salesPromotionDTO.setCouponList(couponBenefitItems);
    }

    /**
     * 构建优惠详情
     *
     * @param salesPromotion 促销配置
     * @param couponList     优惠券集合
     * @param skuIds         skuId集合
     * @return 优惠详情
     */
    private PromotionDetailDTO buildPromotionDetail(SalesPromotionDO salesPromotion, List<BenefitItemDTO> couponList, String skuIds) {
        PromotionDetailDTO detailDTO = BeanCopier.copy(salesPromotion, PromotionDetailDTO::new);
        detailDTO.setSkuIds(skuIds);
        detailDTO.setCouponList(couponList);
        return detailDTO;
    }

}
