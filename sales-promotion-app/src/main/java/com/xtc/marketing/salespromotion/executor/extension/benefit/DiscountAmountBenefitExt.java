package com.xtc.marketing.salespromotion.executor.extension.benefit;

import com.alibaba.cola.extension.Extension;
import com.xtc.marketing.salespromotion.dataobject.SalesPromotionDO;
import com.xtc.marketing.salespromotion.dto.CalculateDTO;
import com.xtc.marketing.salespromotion.dto.PromotionDetailDTO;
import com.xtc.marketing.salespromotion.dto.SalesPromotionDTO;
import com.xtc.marketing.salespromotion.util.BeanCopier;
import com.xtc.marketing.salespromotion.util.GsonUtil;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Extension(useCase = BenefitExtConstant.USE_CASE, scenario = BenefitExtConstant.SCENARIO_DISCOUNT_AMOUNT)
public class DiscountAmountBenefitExt implements BenefitExtPt {

    @Override
    public void calculateSingle(SalesPromotionDO salesPromotion, String paramConfig,
                                CalculateDTO calculateDTO, List<String> skuIds) {
        Param param = GsonUtil.jsonToBean(paramConfig, Param.class);

        // 计算每个sku的单品优惠
        calculateDTO.getCalculateSkus().stream()
                .filter(sku -> skuIds.contains(sku.getSkuId()))
                .forEach(sku -> {
                    // 计算单品优惠
                    sku.addDiscountAmount(param.getDiscountAmount());
                    // 记录优惠详情
                    PromotionDetailDTO promotionDetail = this.buildPromotionDetail(salesPromotion,
                            param.getDiscountAmount(), sku.getSkuId());
                    sku.addOrUpdatePromotionDetail(promotionDetail);
                });

        // 计算总优惠
        calculateDTO.addDiscountAmount(param.getDiscountAmount());
    }

    @Override
    public void calculateCompose(SalesPromotionDO salesPromotion, String paramConfig,
                                 CalculateDTO calculateDTO, List<String> skuIds) {
        Param param = GsonUtil.jsonToBean(paramConfig, Param.class);

        // 计算优惠
        calculateDTO.addDiscountAmount(param.getDiscountAmount());

        // 记录优惠详情
        String skuIdsStr = String.join(",", skuIds);
        PromotionDetailDTO promotionDetail = this.buildPromotionDetail(salesPromotion, param.getDiscountAmount(), skuIdsStr);
        calculateDTO.addOrUpdatePromotionDetail(promotionDetail);
    }

    @Override
    public void setBenefit(SalesPromotionDTO salesPromotionDTO, String paramConfig) {
        Param param = GsonUtil.jsonToBean(paramConfig, Param.class);
        salesPromotionDTO.setDiscountAmount(param.getDiscountAmount());
    }

    /**
     * 构建优惠详情
     *
     * @param salesPromotion 促销配置
     * @param discountAmount 优惠金额
     * @param skuIds         skuId集合
     * @return 优惠详情
     */
    private PromotionDetailDTO buildPromotionDetail(SalesPromotionDO salesPromotion, int discountAmount, String skuIds) {
        PromotionDetailDTO detailDTO = BeanCopier.copy(salesPromotion, PromotionDetailDTO::new);
        detailDTO.setSkuIds(skuIds);
        detailDTO.setDiscountAmount(discountAmount);
        return detailDTO;
    }

    /**
     * 优惠金额参数
     */
    @Getter
    @Setter
    @ToString
    static class Param {

        /**
         * 优惠金额
         */
        private Integer discountAmount;

    }

}
