package com.xtc.marketing.salespromotion;

import com.xtc.marketing.salespromotion.dto.CalculateDTO;
import com.xtc.marketing.salespromotion.dto.SalesPromotionDTO;
import com.xtc.marketing.salespromotion.dto.query.CalculateQry;
import com.xtc.marketing.salespromotion.dto.query.PromotionListQry;
import com.xtc.marketing.salespromotion.executor.query.CalculateQryExe;
import com.xtc.marketing.salespromotion.executor.query.SalesPromotionsQryExe;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@RequiredArgsConstructor
@Service("salesPromotionService")
public class SalesPromotionServiceImpl implements SalesPromotionServiceI {

    private final CalculateQryExe calculateQryExe;
    private final SalesPromotionsQryExe salesPromotionsQryExe;

    @Override
    public CalculateDTO calculate(CalculateQry qry) {
        return calculateQryExe.execute(qry);
    }

    @Override
    public List<SalesPromotionDTO> listSalesPromotions(PromotionListQry qry) {
        return salesPromotionsQryExe.execute(qry);
    }

}
