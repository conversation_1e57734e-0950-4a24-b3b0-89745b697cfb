package com.xtc.marketing.salespromotion.executor.query;

import com.alibaba.cola.extension.BizScenario;
import com.alibaba.cola.extension.ExtensionExecutor;
import com.xtc.marketing.salespromotion.bo.BenefitBO;
import com.xtc.marketing.salespromotion.dataobject.SalesPromotionDO;
import com.xtc.marketing.salespromotion.dto.CalculateDTO;
import com.xtc.marketing.salespromotion.dto.query.CalculateQry;
import com.xtc.marketing.salespromotion.dto.query.SkuQry;
import com.xtc.marketing.salespromotion.enums.PromotionType;
import com.xtc.marketing.salespromotion.exception.BizErrorCode;
import com.xtc.marketing.salespromotion.exception.BizException;
import com.xtc.marketing.salespromotion.exception.SysErrorCode;
import com.xtc.marketing.salespromotion.exception.SysException;
import com.xtc.marketing.salespromotion.executor.checker.RuleChecker;
import com.xtc.marketing.salespromotion.executor.checker.ScopeChecker;
import com.xtc.marketing.salespromotion.executor.extension.benefit.BenefitExtConstant;
import com.xtc.marketing.salespromotion.executor.extension.benefit.BenefitExtPt;
import com.xtc.marketing.salespromotion.repository.SalesPromotionRepository;
import com.xtc.marketing.salespromotion.util.CollectionCopier;
import com.xtc.marketing.salespromotion.util.GsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Component
public class CalculateQryExe {

    private final SalesPromotionRepository salesPromotionRepository;
    private final PromotionLevelQryExe promotionLevelQryExe;
    private final ScopeChecker scopeChecker;
    private final RuleChecker ruleChecker;
    private final ExtensionExecutor extensionExecutor;

    /**
     * 促销计价
     *
     * @param qry 参数
     * @return 促销计价结果
     */
    public CalculateDTO execute(CalculateQry qry) {
        // 检查sku参数是否合法
        this.checkSkuQry(qry.getSkus());

        // 获取促销配置集合
        List<SalesPromotionDO> salesPromotions = this.getPromotions(qry.getBizCode(), qry.getSkus());

        // 初始化促销计价结果
        CalculateDTO calculateDTO = new CalculateDTO(qry);

        // 促销计价，促销配置集合按照顺序排序，先计算单品优惠，然后组合优惠
        salesPromotions.stream()
                .sorted(Comparator.comparing(s -> s.getPromotionType().getSort()))
                .forEach(promotion -> {
                    // 初始化参与促销的sku集合
                    List<SkuQry> promotionSkus = CollectionCopier.copy(qry.getSkus(), SkuQry::new);

                    try {
                        // 过滤符合范围的sku
                        this.filterScopeSkus(promotion, promotionSkus);

                        // 过滤符合规则的sku
                        ruleChecker.check(promotion, promotionSkus);
                    } catch (BizException e) {
                        log.debug("不参与促销活动 promotionToken: {}, msg: {}", promotion.getPromotionToken(), e.getMessage());
                        return;
                    } catch (Exception e) {
                        log.error("促销计价异常 promotionToken: {}", promotion.getPromotionToken(), e);
                        return;
                    }

                    // 汇总参与促销的skuid集合
                    List<String> promotionSkuIds = promotionSkus.stream().map(SkuQry::getSkuId).collect(Collectors.toList());
                    log.info("促销计价 promotionToken: {}, benefit: {}, skus: {}",
                            promotion.getPromotionToken(), promotion.getBenefitConfig(), promotionSkuIds);

                    // 汇总权益
                    try {
                        this.collectBenefit(promotion, calculateDTO, promotionSkuIds);
                    } catch (Exception e) {
                        log.warn("汇总权益异常，不计算优惠 promotionToken: {}", promotion.getPromotionToken(), e);
                    }
                });

        // 校验价格是否正常
        this.checkPriceCorrect(calculateDTO);

        return calculateDTO;
    }

    /**
     * 检查价格是否正确
     *
     * @param calculateDTO 促销计价结果
     */
    public void checkPriceCorrect(CalculateDTO calculateDTO) {
        if (calculateDTO.getPrice() <= 0) {
            throw BizException.of(BizErrorCode.B_CALCULATE_PriceNotCorrect);
        }
        // 计算订单总价
        if (calculateDTO.getPrice() != calculateDTO.getOriginalPrice() - calculateDTO.getSalesPromotionPrice()) {
            throw BizException.of(BizErrorCode.B_CALCULATE_PriceNotCorrect);
        }
        calculateDTO.getCalculateSkus().forEach(sku -> {
            if (sku.getTotalPrice() <= 0) {
                throw BizException.of(BizErrorCode.B_CALCULATE_PriceNotCorrect);
            }
            // 计算sku单价
            if (sku.getUnitPrice() != sku.getSkuUnitPrice() - sku.getSalesPromotionUnitPrice()) {
                throw BizException.of(BizErrorCode.B_CALCULATE_PriceNotCorrect);
            }
            // 计算sku总价
            if (sku.getTotalPrice() != sku.getSkuTotalPrice() - sku.getSalesPromotionTotalPrice()) {
                throw BizException.of(BizErrorCode.B_CALCULATE_PriceNotCorrect);
            }
        });
    }

    /**
     * 汇总权益
     *
     * @param salesPromotion  促销配置
     * @param calculateDTO    促销计价结果
     * @param promotionSkuIds 参与促销的skuid集合
     */
    private void collectBenefit(SalesPromotionDO salesPromotion, CalculateDTO calculateDTO, List<String> promotionSkuIds) {
        List<BenefitBO> benefits = GsonUtil.jsonToList(salesPromotion.getBenefitConfig(), BenefitBO.class);
        benefits.forEach(benefit -> {
            BizScenario benefitScenario = BizScenario.valueOf(BizScenario.DEFAULT_BIZ_ID,
                    BenefitExtConstant.USE_CASE, benefit.getBenefitCode());

            // 对于不同的促销类型执行不同的发放权益的逻辑
            if (salesPromotion.getPromotionType() == PromotionType.SINGLE) {
                extensionExecutor.executeVoid(BenefitExtPt.class, benefitScenario,
                        extension -> extension.calculateSingle(salesPromotion, benefit.getParam(), calculateDTO, promotionSkuIds));
            } else if (salesPromotion.getPromotionType() == PromotionType.COMPOSE) {
                extensionExecutor.executeVoid(BenefitExtPt.class, benefitScenario,
                        extension -> extension.calculateCompose(salesPromotion, benefit.getParam(), calculateDTO, promotionSkuIds));
            }
        });
    }

    /**
     * 过滤符合范围的sku
     *
     * @param salesPromotion 促销配置
     * @param skus           sku集合
     */
    private void filterScopeSkus(SalesPromotionDO salesPromotion, List<SkuQry> skus) {
        skus.removeIf(sku -> {
            try {
                // 确认符合范围
                scopeChecker.check(salesPromotion.getPromotionScope(), sku);
                return false;
            } catch (BizException e) {
                log.debug("不符合促销范围 promotionToken: {}, sku: {}", salesPromotion.getPromotionToken(), sku);
                return true;
            }
        });
        if (skus.isEmpty()) {
            throw BizException.of("所有sku都不符合促销范围 " + salesPromotion.getPromotionScope());
        }
    }

    /**
     * 获取促销配置集合
     *
     * @param bizCode 业务代码
     * @param skus    sku集合
     * @return 促销配置集合
     */
    private List<SalesPromotionDO> getPromotions(String bizCode, List<SkuQry> skus) {
        Set<String> promotionLevel = promotionLevelQryExe.execute(skus);
        return salesPromotionRepository.listEnabledByLevel(bizCode, LocalDateTime.now(), promotionLevel);
    }

    /**
     * 检查sku参数是否合法
     *
     * @param skus sku参数集合
     */
    private void checkSkuQry(List<SkuQry> skus) {
        // 不允许重复的skuId
        long count = skus.stream().map(SkuQry::getSkuId).distinct().count();
        if (count != skus.size()) {
            throw SysException.of(SysErrorCode.S_PARAM_ERROR.getErrCode(),
                    SysErrorCode.S_PARAM_ERROR.getErrDesc() + "：存在重复skuId，请检查");
        }
    }

}
