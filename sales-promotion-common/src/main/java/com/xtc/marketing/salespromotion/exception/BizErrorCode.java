package com.xtc.marketing.salespromotion.exception;

/**
 * 错误码主要有3部分组成：类型+场景+自定义标识
 */
public enum BizErrorCode {

    // 参数异常
//    P_USER_UserIdNotNull("P_USER_UserIdNotNull", "用户id不能为空"),

    // 业务异常 - 促销计价
    B_CALCULATE_PriceNotCorrect("B_CALCULATE_PriceNotCorrect", "计价异常，请稍后重试"),
    ;

    private final String errCode;
    private final String errDesc;

    BizErrorCode(String errCode, String errDesc) {
        this.errCode = errCode;
        this.errDesc = errDesc;
    }

    public String getErrCode() {
        return errCode;
    }

    public String getErrDesc() {
        return errDesc;
    }

}
