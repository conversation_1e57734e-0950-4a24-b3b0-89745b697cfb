package com.xtc.marketing.salespromotion.util;

import java.math.BigDecimal;

/**
 * 金钱工具类
 * <p>禁止使用构造方法 BigDecimal(double) 的方式直接把 double 值转为 BigDecimal 对象
 */
public class MoneyUtil {

    private MoneyUtil() {
    }

    /**
     * 保留 2 位小数
     */
    private static final int SCALE_TWO = 2;

    /**
     * 分转元
     *
     * @param cent 分
     * @return 元
     */
    public static String centToYuanString(int cent) {
        BigDecimal yuan = new BigDecimal(cent).movePointLeft(SCALE_TWO);
        return yuan.toPlainString();
    }

    /**
     * 分转元
     *
     * @param cent 分
     * @return 元
     */
    public static BigDecimal centToYuan(int cent) {
        return new BigDecimal(cent).movePointLeft(SCALE_TWO);
    }

    /**
     * 元转分
     *
     * @param yuan 元
     * @return 分
     */
    public static int yuanToCent(String yuan) {
        return new BigDecimal(yuan).movePointRight(SCALE_TWO).intValue();
    }

    /**
     * 元转分
     *
     * @param yuan 元
     * @return 分
     */
    public static int yuanToCent(BigDecimal yuan) {
        return yuan.movePointRight(SCALE_TWO).intValue();
    }

}
