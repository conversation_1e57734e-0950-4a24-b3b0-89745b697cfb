package com.xtc.marketing.salespromotion.cache;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.*;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.concurrent.TimeUnit;
import java.util.function.BooleanSupplier;
import java.util.function.Supplier;

@Slf4j
@Component
public class RedissonUtil {

    /**
     * lock 默认等待获取锁的时间
     */
    private static final long LOCK_LESS_SECONDS = 10;
    /**
     * tryLock 默认等待获取锁的时间
     */
    private static final long TRY_LOCK_WAIT_SECONDS = 3;
    /**
     * tryLock 默认锁的持续时间
     */
    private static final long TRY_LOCK_LESS_SECONDS = 5;

    @Resource
    private RedissonClient redissonClient;

    /**
     * 自增
     *
     * @param key    key
     * @param number 自增值
     * @return long
     */
    public Long incr(String key, long number) {
        return redissonClient.getAtomicLong(key).addAndGet(number);
    }

    public Long incr(String key, Duration duration) {
        RAtomicLong atomicLong = redissonClient.getAtomicLong(key);
        long incr = atomicLong.incrementAndGet();
        atomicLong.expireIfNotSet(duration);
        return incr;
    }

    /**
     * 设置过期时间
     *
     * @param key      key
     * @param duration duration类型
     */
    public void expire(String key, Duration duration) {
        redissonClient.getBucket(key).expire(duration);
    }

    /**
     * 设置值（无超时）
     *
     * @param key   键
     * @param value 值
     */
    public <V> void set(String key, V value) {
        redissonClient.getBucket(key).set(value);
    }

    /**
     * 设置值时同时设置超时时间（单位为秒）
     *
     * @param key     键
     * @param value   值
     * @param timeout 超时时间（秒）
     */
    public <V> void set(String key, V value, long timeout) {
        redissonClient.getBucket(key).set(value, timeout, TimeUnit.SECONDS);
    }

    /**
     * 设置值时同时设置超时时间，并指定超时时间的单位
     *
     * @param key      键
     * @param value    值
     * @param timeout  超时时间
     * @param timeUnit 时间单位
     */
    public <V> void set(String key, V value, long timeout, TimeUnit timeUnit) {
        redissonClient.getBucket(key).set(value, timeout, timeUnit);
    }

    /**
     * 根据键获取值
     *
     * @param key 键
     * @return V 值
     */
    public <V> V get(String key, Class<V> clazz) {
        RBucket<V> bucket = redissonClient.getBucket(key);
        return bucket.get();
    }

    /**
     * 删除缓存
     *
     * @param key key
     */
    public void delete(String key) {
        redissonClient.getBucket(key).delete();
    }

    /**
     * 创建流量限流器，并尝试获取许可
     *
     * @param bizName      业务名称
     * @param bizId        业务id
     * @param rate         允许流量
     * @param rateInterval 限制时间间隔
     * @return 执行结果
     */
    public boolean rateLimiter(@NonNull String bizName, @NonNull String bizId, long rate, long rateInterval) {
        if (StringUtils.isBlank(bizName)) {
            log.error("RedissonUtil rateLimiter - 业务名称为空");
            return false;
        }
        if (StringUtils.isBlank(bizId)) {
            log.error("RedissonUtil rateLimiter - 业务id为空");
            return false;
        }

        // 创建流量限流器
        String name = "RedissonLock:" + bizName + ":" + bizId;
        RRateLimiter rateLimiter = redissonClient.getRateLimiter(name);
        log.debug("RedissonUtil getRateLimiter - name: {}", rateLimiter.getName());

        // 尝试获取许可
        rateLimiter.trySetRate(RateType.OVERALL, rate, rateInterval, RateIntervalUnit.SECONDS);
        boolean tryAcquire = rateLimiter.tryAcquire(1);

        // 设置过期时间为限制时间的两倍，减少重复创建缓存次数
        // rateLimiter.expire 要放在 rateLimiter.tryAcquire 后面才起作用
        rateLimiter.expire(Duration.ofSeconds(rateInterval * 2));

        return tryAcquire;
    }

    /**
     * 加锁，执行业务
     * <p>默认锁的持续时间10s</p>
     *
     * @param bizName   业务名称
     * @param bizId     业务id
     * @param onSuccess 加锁成功时执行
     * @param onFailure 加锁失败时抛出的异常
     */
    public void lock(@NonNull String bizName, @NonNull String bizId, Runnable onSuccess, RuntimeException onFailure) {
        RLock lock = getLock(bizName, bizId);
        if (lock == null) {
            throw onFailure;
        }

        try {
            // 第一个参数表示锁的持续时间
            lock.lock(LOCK_LESS_SECONDS, TimeUnit.SECONDS);
            log.debug("RedissonUtil lock - lockKey: {}", lock.getName());
            onSuccess.run();
        } finally {
            lock.unlock();
        }
    }

    /**
     * 加锁，执行业务并返回值
     * <p>默认获取等待时间3s，锁的持续时间5s</p>
     *
     * @param bizName   业务名称
     * @param bizId     业务id
     * @param onSuccess 加锁成功时执行
     * @param onFailure 加锁失败时抛出的异常
     * @return T 业务返回值
     */
    public <T> T lock(@NonNull String bizName, @NonNull String bizId, Supplier<T> onSuccess, RuntimeException onFailure) {
        RLock lock = getLock(bizName, bizId);
        if (lock == null) {
            throw onFailure;
        }

        try {
            // 第一个参数表示锁的持续时间
            lock.lock(LOCK_LESS_SECONDS, TimeUnit.SECONDS);
            log.debug("RedissonUtil lock - lockKey: {}", lock.getName());
            return onSuccess.get();
        } finally {
            lock.unlock();
        }
    }

    /**
     * 加锁，执行业务
     * <p>默认获取等待时间3s，锁的持续时间5s</p>
     *
     * @param bizName   业务名称
     * @param bizId     业务id
     * @param onSuccess 加锁成功时执行
     * @param onFailure 加锁失败时抛出的异常
     */
    public void tryLockAndWait(@NonNull String bizName, @NonNull String bizId, Runnable onSuccess, RuntimeException onFailure) {
        RLock lock = getLock(bizName, bizId);
        if (lock == null) {
            throw onFailure;
        }

        try {
            // 第一个参数表示尝试获取分布式锁，并且最大的等待获取锁的时间，第二个参数表示锁的持续时间
            boolean tryLock = lock.tryLock(TRY_LOCK_WAIT_SECONDS, TRY_LOCK_LESS_SECONDS, TimeUnit.SECONDS);
            log.debug("RedissonUtil tryLock - tryLock: {}, lockKey: {}", tryLock, lock.getName());
            if (tryLock) {
                onSuccess.run();
            } else {
                throw onFailure;
            }
        } catch (InterruptedException e) {
            log.error("RedissonUtil tryLock - throw InterruptedException lockKey: {}", lock.getName());
            onFailure.initCause(e);
            throw onFailure;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 加锁，执行业务
     * <p>默认获取等待时间3s，锁的持续时间5s</p>
     *
     * @param bizName   业务名称
     * @param bizId     业务id
     * @param onSuccess 加锁成功时执行，执行失败后抛出的异常
     * @param onFailure 加锁失败时抛出的异常
     */
    public void tryLockAndWait(@NonNull String bizName, @NonNull String bizId, BooleanSupplier onSuccess, RuntimeException onFailure) {
        RLock lock = getLock(bizName, bizId);
        if (lock == null) {
            throw onFailure;
        }

        try {
            // 第一个参数表示尝试获取分布式锁，并且最大的等待获取锁的时间，第二个参数表示锁的持续时间
            boolean tryLock = lock.tryLock(TRY_LOCK_WAIT_SECONDS, TRY_LOCK_LESS_SECONDS, TimeUnit.SECONDS);
            log.debug("RedissonUtil tryLock - tryLock: {}, lockKey: {}", tryLock, lock.getName());
            if (!tryLock || !onSuccess.getAsBoolean()) {
                throw onFailure;
            }
        } catch (InterruptedException e) {
            log.error("RedissonUtil tryLock - throw InterruptedException lockKey: {}", lock.getName());
            onFailure.initCause(e);
            throw onFailure;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 加锁，执行业务并返回值
     * <p>默认获取等待时间3s，锁的持续时间5s</p>
     *
     * @param bizName   业务名称
     * @param bizId     业务id
     * @param onSuccess 加锁成功时执行
     * @param onFailure 加锁失败时抛出的异常
     * @return T 业务返回值
     */
    public <T> T tryLockAndWait(@NonNull String bizName, @NonNull String bizId, Supplier<T> onSuccess, RuntimeException onFailure) {
        RLock lock = getLock(bizName, bizId);
        if (lock == null) {
            throw onFailure;
        }

        try {
            // 第一个参数表示尝试获取分布式锁，并且最大的等待获取锁的时间，第二个参数表示锁的持续时间
            boolean tryLock = lock.tryLock(TRY_LOCK_WAIT_SECONDS, TRY_LOCK_LESS_SECONDS, TimeUnit.SECONDS);
            log.debug("RedissonUtil tryLock - tryLock: {}, lockKey: {}", tryLock, lock.getName());
            if (tryLock) {
                return onSuccess.get();
            } else {
                throw onFailure;
            }
        } catch (InterruptedException e) {
            log.error("RedissonUtil tryLock - throw InterruptedException lockKey: {}", lock.getName());
            onFailure.initCause(e);
            throw onFailure;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 加锁，执行业务并返回值
     * <p>默认获取等待时间3s，锁的持续时间5s</p>
     *
     * @param bizName   业务名称
     * @param bizId     业务id
     * @param onSuccess 加锁成功时执行
     * @param onFailure 加锁失败时执行
     * @return T 业务返回值
     */
    public <T> T tryLockAndWait(@NonNull String bizName, @NonNull String bizId, Supplier<T> onSuccess, Supplier<T> onFailure) {
        RLock lock = getLock(bizName, bizId);
        if (lock == null) {
            return onFailure.get();
        }

        try {
            // 第一个参数表示尝试获取分布式锁，并且最大的等待获取锁的时间，第二个参数表示锁的持续时间
            boolean tryLock = lock.tryLock(TRY_LOCK_WAIT_SECONDS, TRY_LOCK_LESS_SECONDS, TimeUnit.SECONDS);
            log.debug("RedissonUtil tryLock - tryLock: {}, lockKey: {}", tryLock, lock.getName());
            if (tryLock) {
                return onSuccess.get();
            } else {
                return onFailure.get();
            }
        } catch (InterruptedException e) {
            log.error("RedissonUtil tryLock - throw InterruptedException lockKey: {}", lock.getName());
            return onFailure.get();
        } finally {
            lock.unlock();
        }
    }

    /**
     * 尝试加锁，执行业务
     * <p>默认锁的持续时间30s</p>
     *
     * @param bizName   业务名称
     * @param bizId     业务id
     * @param onSuccess 加锁成功时执行
     * @param onFailure 加锁失败时抛出的异常
     */
    public void tryLock(@NonNull String bizName, @NonNull String bizId, Runnable onSuccess, RuntimeException onFailure) {
        RLock lock = getLock(bizName, bizId);
        if (lock == null) {
            throw onFailure;
        }

        boolean tryLock = lock.tryLock();
        log.debug("RedissonUtil tryLock - tryLock: {}, bizName: {}, bizId: {}", tryLock, bizName, bizId);
        if (tryLock) {
            try {
                onSuccess.run();
            } finally {
                lock.unlock();
            }
        } else {
            throw onFailure;
        }
    }

    /**
     * 尝试加锁，执行业务并返回值
     * <p>默认锁的持续时间30s</p>
     *
     * @param bizName          业务名称
     * @param bizId            业务id
     * @param onSuccess        加锁成功时执行
     * @param onFailureDefault 加锁失败时默认返回
     * @return T 业务返回值
     */
    public <T> T tryLock(@NonNull String bizName, @NonNull String bizId, Supplier<T> onSuccess, T onFailureDefault) {
        RLock lock = getLock(bizName, bizId);
        if (lock == null) {
            return onFailureDefault;
        }

        boolean tryLock = lock.tryLock();
        log.debug("RedissonUtil tryLock - tryLock: {}, bizName: {}, bizId: {}", tryLock, bizName, bizId);
        if (tryLock) {
            try {
                return onSuccess.get();
            } finally {
                lock.unlock();
            }
        } else {
            return onFailureDefault;
        }
    }

    /**
     * 获取锁
     *
     * @param bizName 业务名称
     * @param bizId   业务id
     * @return 锁
     */
    public RLock getLock(@NonNull String bizName, @NonNull String bizId) {
        if (StringUtils.isBlank(bizName)) {
            log.error("RedissonUtil getLock - 业务名称为空");
            return null;
        }
        if (StringUtils.isBlank(bizId)) {
            log.error("RedissonUtil getLock - 业务id为空");
            return null;
        }

        String name = "RedissonLock:" + bizName + ":" + bizId;
        RLock lock = redissonClient.getLock(name);
        log.debug("RedissonUtil getLock - name: {}", lock.getName());
        return lock;
    }

    /**
     * 创建布隆过滤器
     *
     * @param name               过滤器名称
     * @param expectedInsertions 预期数据量
     * @param falseProbability   错误率
     * @return 布隆过滤器
     */
    public RBloomFilter<String> getBloomFilter(String name, long expectedInsertions, double falseProbability) {
        RBloomFilter<String> bloomFilter = redissonClient.getBloomFilter(name);
        bloomFilter.tryInit(expectedInsertions, falseProbability);
        return bloomFilter;
    }

}
