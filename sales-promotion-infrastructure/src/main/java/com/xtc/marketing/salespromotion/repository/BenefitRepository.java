package com.xtc.marketing.salespromotion.repository;

import com.xtc.marketing.salespromotion.config.BaseRepository;
import com.xtc.marketing.salespromotion.dataobject.BenefitDO;
import com.xtc.marketing.salespromotion.repository.mapper.BenefitMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class BenefitRepository extends BaseRepository<BenefitMapper, BenefitDO> {

    /**
     * 查询已开启的权益集合
     *
     * @return 权益集合
     */
    public List<BenefitDO> listEnabled() {
        return this.listBy(BenefitDO::getEnabled, true);
    }

}
