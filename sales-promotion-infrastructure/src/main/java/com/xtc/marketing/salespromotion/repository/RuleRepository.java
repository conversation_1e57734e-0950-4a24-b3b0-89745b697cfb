package com.xtc.marketing.salespromotion.repository;

import com.xtc.marketing.salespromotion.config.BaseRepository;
import com.xtc.marketing.salespromotion.dataobject.RuleDO;
import com.xtc.marketing.salespromotion.repository.mapper.RuleMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class RuleRepository extends BaseRepository<RuleMapper, RuleDO> {

    /**
     * 查询已开启的规则集合
     *
     * @return 规则集合
     */
    public List<RuleDO> listEnabled() {
        return this.listBy(RuleDO::getEnabled, true);
    }

}
