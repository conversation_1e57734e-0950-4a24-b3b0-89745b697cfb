package com.xtc.marketing.salespromotion.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.xtc.marketing.salespromotion.config.BaseDO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@ToString
@NoArgsConstructor
@SuperBuilder
@TableName("t_scope_template")
public class ScopeTemplateDO extends BaseDO {

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 模板描述
     */
    private String templateDesc;

    /**
     * 商品范围
     */
    private Object itemScope;

    /**
     * 代理范围
     */
    private Object agentScope;

    /**
     * 门店范围
     */
    private Object shopScope;

}
