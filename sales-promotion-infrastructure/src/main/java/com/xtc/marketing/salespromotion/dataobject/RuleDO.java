package com.xtc.marketing.salespromotion.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.xtc.marketing.salespromotion.config.BaseDO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@ToString
@NoArgsConstructor
@SuperBuilder
@TableName("t_rule")
public class RuleDO extends BaseDO {

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 规则描述
     */
    private String ruleDesc;

    /**
     * 规则代码
     */
    private String ruleCode;

    /**
     * 启用
     */
    private Boolean enabled;

    /**
     * 参数配置
     */
    private String paramConfig;

}
