package com.xtc.marketing.salespromotion.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.xtc.marketing.salespromotion.config.BaseDO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@ToString
@NoArgsConstructor
@SuperBuilder
@TableName("t_benefit")
public class BenefitDO extends BaseDO {

    /**
     * 权益名称
     */
    private String benefitName;

    /**
     * 权益描述
     */
    private String benefitDesc;

    /**
     * 权益代码
     */
    private String benefitCode;

    /**
     * 启用
     */
    private Boolean enabled;

    /**
     * 参数配置
     */
    private String paramConfig;

}
