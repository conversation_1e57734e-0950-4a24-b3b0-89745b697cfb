package com.xtc.marketing.salespromotion.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import com.xtc.marketing.salespromotion.config.BaseDO;
import com.xtc.marketing.salespromotion.enums.PromotionType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

@Getter
@Setter
@ToString
@NoArgsConstructor
@SuperBuilder
@TableName("t_sales_promotion")
public class SalesPromotionDO extends BaseDO {

    /**
     * 代理代码
     */
    private String agentCode;

    /**
     * 促销编号
     */
    private String promotionToken;

    /**
     * 促销类型
     */
    private PromotionType promotionType;

    /**
     * 促销名称
     */
    private String promotionName;

    /**
     * 促销描述
     */
    private String promotionDesc;

    /**
     * 启用
     */
    private Boolean enabled;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 规则描述
     */
    private String ruleDesc;

    /**
     * 业务代码
     */
    private String bizCode;

    /**
     * 促销级别（工厂、代理、二代、门店）
     * <ul>
     * <li>工厂：FACTORY
     * <li>代理：代理代码
     * <li>二代：二级代理代码
     * <li>门店：门店编号
     * </ul>
     */
    private String promotionLevel;

    /**
     * 促销范围
     */
    private String promotionScope;

    /**
     * 规则配置
     */
    private String ruleConfig;

    /**
     * 权益配置
     */
    private String benefitConfig;

    /**
     * 页面配置（业务定义）
     */
    private String webConfig;

    /**
     * 是否是积分权益促销
     */
    private Boolean pointBenefit;

}
