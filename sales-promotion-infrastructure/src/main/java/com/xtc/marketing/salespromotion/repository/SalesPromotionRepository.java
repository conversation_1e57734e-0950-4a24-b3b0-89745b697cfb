package com.xtc.marketing.salespromotion.repository;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xtc.marketing.salespromotion.config.BaseRepository;
import com.xtc.marketing.salespromotion.dataobject.SalesPromotionDO;
import com.xtc.marketing.salespromotion.repository.mapper.SalesPromotionMapper;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@Repository
public class SalesPromotionRepository extends BaseRepository<SalesPromotionMapper, SalesPromotionDO> {

    /**
     * 查询已开启的促销配置列表
     *
     * @param bizCode 业务代码
     * @param time    时间
     * @param level   促销级别
     * @return 促销配置列表
     */
    public List<SalesPromotionDO> listEnabledByLevel(String bizCode, LocalDateTime time, Set<String> level) {
        Wrapper<SalesPromotionDO> wrapper = Wrappers.<SalesPromotionDO>lambdaQuery()
                .eq(SalesPromotionDO::getBizCode, bizCode)
                .eq(SalesPromotionDO::getEnabled, true)
                .le(SalesPromotionDO::getStartTime, time)
                .ge(SalesPromotionDO::getEndTime, time)
                .in(SalesPromotionDO::getPromotionLevel, level)
                .orderByDesc(SalesPromotionDO::getId)
                .last(BaseRepository.DEFAULT_LIST_SIZE_LIMIT);
        return this.list(wrapper);
    }

}
