spring:
  # servlet 预热，取消懒加载
  mvc:
    servlet:
      load-on-startup: 1
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: sales_promotion
    password: 6wdnN3tJ09CUbXneBq1r
    url: ***************************************************************************************************************************************************************************************
  redis:
    host: xtc-marketing-service-new.redis.rds.aliyuncs.com
    port: 6379
    username: sales_promotion
    password: 4lgAuhOOeLHzceY2gz6f
