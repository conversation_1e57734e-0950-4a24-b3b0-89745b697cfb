nacos:
  config:
    # 开启配置预加载功能
    bootstrap:
      enable: true
      log-enable: true
    # 开启自动刷新
    auto-refresh: true
    # 远程配置优先
    remote-first: true
    server-addr: mse-9b2bc7a2-p.nacos-ans.mse.aliyuncs.com
    access-key: LTAI5tCZ3b9TZff1aMABH5hR
    secret-key: ******************************
    type: yaml
    namespace: marketing-service-env-${spring.profiles.active}
    group: DEFAULT_GROUP
    data-ids: marketing-actuator.yaml,marketing-arthas.yaml,marketing-feign.yaml
