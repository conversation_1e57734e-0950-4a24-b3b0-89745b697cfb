spring:
  datasource:
    druid:
      # druid中文文档：https://github.com/alibaba/druid/wiki/%E9%A6%96%E9%A1%B5
      # 初始化大小，最小，最大
      initial-size: 4
      min-idle: 4
      # 连接数 = (核心数 * 2) + 有效磁盘数，常用配置：(核心数 * 2) + 1
      max-active: 10
      # 配置获取连接等待超时的时间
      max-wait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      time-between-eviction-runs-millis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      min-evictable-idle-time-millis: 300000
      # 用来检测连接是否有效的sql
      validation-query: select 1 from dual
      # 申请连接的时候检测，如果空闲时间大于time-between-eviction-runs-millis，执行validation-query检测连接是否有效
      test-while-idle: true
      # 申请连接时执行validation-query检测连接是否有效，做了这个配置会降低性能。
      test-on-borrow: false
      # 归还连接时执行validation-query检测连接是否有效，做了这个配置会降低性能。
      test-on-return: false
