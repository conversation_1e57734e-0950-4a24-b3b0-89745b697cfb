package com.xtc.marketing.salespromotion.intercepter;

import com.xtc.marketing.salespromotion.annotation.RateLimiter;
import com.xtc.marketing.salespromotion.cache.RedissonUtil;
import com.xtc.marketing.salespromotion.config.RequestBodyReaderWrapper;
import com.xtc.marketing.salespromotion.constant.SystemConstant;
import com.xtc.marketing.salespromotion.exception.SysErrorCode;
import com.xtc.marketing.salespromotion.exception.SysException;
import com.xtc.marketing.salespromotion.util.AnnotationCatcher;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.resource.ResourceHttpRequestHandler;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.Map;

@Slf4j
@RequiredArgsConstructor
@Component
public class RateLimiterInterceptor implements HandlerInterceptor {

    private final RedissonUtil redissonUtil;

    @Override
    public boolean preHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull Object handler) {
        // 过滤资源请求
        if (handler instanceof ResourceHttpRequestHandler) {
            return true;
        }

        // 判断流量限流器注解存在
        HandlerMethod handlerMethod = (HandlerMethod) handler;
        Method method = handlerMethod.getMethod();
        RateLimiter rateLimiter = AnnotationCatcher.catchAnnotation(method, RateLimiter.class);
        if (rateLimiter == null || "".equals(rateLimiter.limitParam())) {
            return true;
        }

        // 获取限制参数
        Map<String, Object> allParams = new RequestBodyReaderWrapper(request).getAllParams();
        Object limitParam = allParams.get(rateLimiter.limitParam());
        if (limitParam == null || StringUtils.isBlank(limitParam.toString())) {
            return true;
        }

        // 流量限流器获取许可
        String bizName = SystemConstant.SYSTEM_NAME + ":rate-limiter:"
                + method.getDeclaringClass().getName() + ":" + method.getName();
        int rate = rateLimiter.rate() > 0 ? rateLimiter.rate() : 1;
        int rateInterval = rateLimiter.rateInterval() > 0 ? rateLimiter.rateInterval() : 1;
        boolean tryAcquire = redissonUtil.rateLimiter(bizName, limitParam.toString(), rate, rateInterval);
        if (!tryAcquire) {
            throw SysException.of(SysErrorCode.S_RATELIMITER_RateLimiting);
        }
        return true;
    }

}
