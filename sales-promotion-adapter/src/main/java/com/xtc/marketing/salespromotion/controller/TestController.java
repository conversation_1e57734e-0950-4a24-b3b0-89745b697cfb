package com.xtc.marketing.salespromotion.controller;

import com.xtc.marketing.salespromotion.exception.Response;
import lombok.RequiredArgsConstructor;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.Duration;

/**
 * 测试
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/api")
public class TestController {

    private final JdbcTemplate jdbcTemplate;

    @GetMapping("/test")
    public Response test() {
        return Response.buildSuccess();
    }

    @GetMapping("/testSql")
    public Response testSql() {
        jdbcTemplate.execute("select 1");
        return Response.buildSuccess();
    }

    @GetMapping("/test1")
    public Response test1() {
        try {
            Thread.sleep(Duration.ofSeconds(10).toMillis());
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        return Response.buildSuccess();
    }

    @GetMapping("/test2")
    public Response test2() {
        try {
            Thread.sleep(Duration.ofSeconds(20).toMillis());
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        return Response.buildSuccess();
    }

    @GetMapping("/test3")
    public Response test3() {
        try {
            Thread.sleep(Duration.ofSeconds(30).toMillis());
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        return Response.buildSuccess();
    }

    @GetMapping("/test6")
    public Response test6() {
        try {
            Thread.sleep(Duration.ofSeconds(60).toMillis());
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        return Response.buildSuccess();
    }

}
