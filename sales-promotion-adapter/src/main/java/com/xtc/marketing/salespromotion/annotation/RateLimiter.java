package com.xtc.marketing.salespromotion.annotation;

import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;

import java.lang.annotation.*;

/**
 * 流量限流器
 * <p>限制参数，默认：buyerId</p>
 * <p>限制流量，默认1</p>
 * <p>限制时间间隔，默认1s</p>
 */
@Documented
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Order(Ordered.HIGHEST_PRECEDENCE)
public @interface RateLimiter {

    /**
     * 限制参数，默认：buyerId
     * <p>限制参数的值为空时，不执行限流操作</p>
     */
    String limitParam() default "buyerId";

    /**
     * 限制流量，默认1
     */
    int rate() default 1;

    /**
     * 限制时间间隔，默认1s
     */
    int rateInterval() default 1;

}
