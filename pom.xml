<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.xtc.marketing.salespromotion</groupId>
    <artifactId>sales-promotion-parent</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>
    <name>sales-promotion</name>

    <properties>
        <!--System-->
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>${maven.compiler.source}</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

        <!--Misc-->
        <druid-starter.version>1.2.15</druid-starter.version>
        <mybatis-starter.version>2.2.2</mybatis-starter.version>
        <mybatis-plus-starter.version>3.5.2</mybatis-plus-starter.version>
        <redisson-starter.version>3.20.1</redisson-starter.version>
        <lombok.version>1.18.24</lombok.version>
        <mapstruct.version>1.5.3.Final</mapstruct.version>
        <commons-lang3.version>3.12.0</commons-lang3.version>
        <commons-collections.version>4.4</commons-collections.version>
        <guava.version>31.1-jre</guava.version>
        <gson.version>2.10</gson.version>
        <arthas-starter.version>3.6.7</arthas-starter.version>
        <nacos-starter.version>0.2.12</nacos-starter.version>

        <!--Framework-->
        <sales-promotion-client.version>1.0.3</sales-promotion-client.version>
        <marketing-components-dto.version>1.0.0</marketing-components-dto.version>
        <cola.components.version>4.3.1</cola.components.version>
        <spring-cloud.version>2021.0.5</spring-cloud.version>
        <spring-boot.version>2.7.6</spring-boot.version>
        <start-class>com.xtc.marketing.salespromotion.Application</start-class>
    </properties>

    <modules>
        <module>sales-promotion-client</module>
        <module>sales-promotion-adapter</module>
        <module>sales-promotion-app</module>
        <module>sales-promotion-domain</module>
        <module>sales-promotion-infrastructure</module>
        <module>sales-promotion-common</module>
        <module>start</module>
    </modules>

    <repositories>
        <!--阿里云镜像库-->
        <repository>
            <id>alimaven</id>
            <name>aliyun maven</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <layout>default</layout>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <!--公司内部库-->
        <repository>
            <id>maven-public</id>
            <url>http://packages.bbkedu.com/repository/maven-public/</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>fail</checksumPolicy>
            </releases>
        </repository>
    </repositories>

    <dependencyManagement>
        <dependencies>
            <!--Project modules-->
            <dependency>
                <groupId>com.xtc.marketing.salespromotion</groupId>
                <artifactId>sales-promotion-adapter</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xtc.marketing.salespromotion</groupId>
                <artifactId>sales-promotion-client</artifactId>
                <version>${sales-promotion-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xtc.marketing.salespromotion</groupId>
                <artifactId>sales-promotion-app</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xtc.marketing.salespromotion</groupId>
                <artifactId>sales-promotion-domain</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xtc.marketing.salespromotion</groupId>
                <artifactId>sales-promotion-infrastructure</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xtc.marketing.salespromotion</groupId>
                <artifactId>sales-promotion-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!--Project modules End-->

            <!--COLA Components-->
            <dependency>
                <groupId>com.alibaba.cola</groupId>
                <artifactId>cola-components-bom</artifactId>
                <version>${cola.components.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--COLA Components End-->

            <!--Marketing Components-->
            <dependency>
                <groupId>com.xtc.marketing</groupId>
                <artifactId>marketing-component-dto</artifactId>
                <version>${marketing-components-dto.version}</version>
            </dependency>
            <!--Marketing Components End-->

            <!--Spring Boot-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson-starter.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.redisson</groupId>
                        <artifactId>redisson-spring-data-30</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-data-27</artifactId>
                <version>${redisson-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.arthas</groupId>
                <artifactId>arthas-spring-boot-starter</artifactId>
                <version>${arthas-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.boot</groupId>
                <artifactId>nacos-config-spring-boot-starter</artifactId>
                <version>${nacos-starter.version}</version>
            </dependency>
            <!--Spring Boot End-->

            <!--Spring Cloud-->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--Spring Cloud End-->

            <!--Datasource-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus-starter.version}</version>
            </dependency>
            <!--Datasource End-->

            <!--Misc-->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>${commons-collections.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${gson.version}</version>
            </dependency>
            <!--Misc End-->
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>3.3.0</version>
                </plugin>
                <plugin>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.10.1</version>
                    <configuration>
                        <annotationProcessorPaths>
                            <!--必须要添加 lombok 的 path，解决和 mapstruct 的冲突-->
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>${lombok.version}</version>
                            </path>
                            <path>
                                <groupId>org.mapstruct</groupId>
                                <artifactId>mapstruct-processor</artifactId>
                                <version>${mapstruct.version}</version>
                            </path>
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>
                <plugin>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.2.1</version>
                    <!--发布源码-->
                    <executions>
                        <execution>
                            <id>attach-sources</id>
                            <phase>package</phase>
                            <goals>
                                <goal>jar-no-fork</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>3.4.0</version>
                </plugin>
                <plugin>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>3.0.0</version>
                </plugin>

                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                    <configuration>
                        <mainClass>${start-class}</mainClass>
                        <includeSystemScope>true</includeSystemScope>
                    </configuration>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>maven-releases</id>
            <name>maven-releases</name>
            <url>http://packages.bbkedu.com/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>maven-snapshots</id>
            <name>maven-snapshots</name>
            <url>http://packages.bbkedu.com/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
</project>