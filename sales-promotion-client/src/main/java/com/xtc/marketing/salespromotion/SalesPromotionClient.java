package com.xtc.marketing.salespromotion;

import com.xtc.marketing.marketingcomponentdto.dto.MultiResponse;
import com.xtc.marketing.marketingcomponentdto.dto.SingleResponse;
import com.xtc.marketing.salespromotion.dto.CalculateDTO;
import com.xtc.marketing.salespromotion.dto.SalesPromotionDTO;
import com.xtc.marketing.salespromotion.dto.query.CalculateQry;
import com.xtc.marketing.salespromotion.dto.query.PromotionListQry;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(
        contextId = "salesPromotionClient",
        name = "sales-promotion-feign",
        url = "${xtc.feign.client.sales-promotion.url}"
)
public interface SalesPromotionClient {

    /**
     * 促销计价
     *
     * @param qry 参数
     * @return 促销计价结果
     */
    @PostMapping("/api/sales-promotion/calculate")
    SingleResponse<CalculateDTO> calculate(@RequestBody CalculateQry qry);

    /**
     * 查询促销列表
     *
     * @param qry 参数
     * @return 促销列表
     */
    @GetMapping("/api/sales-promotion/promotions")
    MultiResponse<SalesPromotionDTO> listPromotions(@SpringQueryMap PromotionListQry qry);

}
