package com.xtc.marketing.salespromotion.dto.query;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 促销计价参数
 */
@Data
public class CalculateQry {

    /**
     * 业务代码
     */
    @NotBlank
    @Length(max = 50)
    private String bizCode;

    /**
     * 买家id（会针对买家id配置限流器）
     */
    @Length(max = 100)
    private String buyerId;

    /**
     * sku集合
     * <p>不允许重复的skuId</p>
     */
    @NotNull
    @Size(min = 1, max = 20)
    private List<SkuQry> skus;

}
