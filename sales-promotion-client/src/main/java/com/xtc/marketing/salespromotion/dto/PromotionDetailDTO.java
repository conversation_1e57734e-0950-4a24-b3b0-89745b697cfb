package com.xtc.marketing.salespromotion.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 促销详情
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PromotionDetailDTO {

    /**
     * 促销编号
     */
    private String promotionToken;

    /**
     * 促销名称
     */
    private String promotionName;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 规则描述
     */
    private String ruleDesc;

    /**
     * 促销金额（单位：分）
     */
    private Integer discountAmount;

    /**
     * 积分数量
     */
    private Integer point;

    /**
     * 积分倍数
     */
    private Integer pointMultiple;

    /**
     * 优惠券集合
     */
    private List<BenefitItemDTO> couponList;

    /**
     * 赠品sku集合
     */
    private List<BenefitItemDTO> giftSkuList;

    /**
     * skuId集合
     */
    private String skuIds;

    /**
     * 是否是积分权益促销
     */
    private Boolean pointBenefit;

    /**
     * 使用的积分数量（用于积分抵扣场景）
     */
    private Integer usingPoints;

}
