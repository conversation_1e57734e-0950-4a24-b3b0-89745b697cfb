package com.xtc.marketing.salespromotion.dto.query;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;

@Data
public class SkuQry {

    /**
     * 代理代码
     */
    @Length(max = 100)
    private String agentCode;

    /**
     * 二级代理代码
     */
    @Length(max = 100)
    private String secondAgentCode;

    /**
     * 门店id
     */
    @NotBlank
    @Length(max = 100)
    private String shopId;

    /**
     * 商品id
     */
    @Length(max = 100)
    private String productId;

    /**
     * skuid
     */
    @NotBlank
    @Length(max = 100)
    private String skuId;

    /**
     * sku单价（单位：分）
     */
    @NotNull
    @Positive
    @Max(9999999)
    private Integer skuUnitPrice;

    /**
     * sku数量
     */
    @NotNull
    @Positive
    @Max(100)
    private Integer skuNum;

    /**
     * 使用积分
     */
    @Max(9999999)
    private Integer usingPoints;

}
