package com.xtc.marketing.salespromotion.dto.query;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class PromotionListQry extends SkuQry {

    /**
     * 业务代码
     */
    @NotBlank
    @Length(max = 50)
    private String bizCode;

}
